<?php

namespace App\Controllers;

use CodeIgniter\API\ResponseTrait;

class IncomingApplicantsController extends BaseController
{
    use ResponseTrait;

    protected $session;
    protected $applicationsModel;

    public function __construct()
    {
        helper(['form', 'url']);
        $this->session = \Config\Services::session();
        $this->applicationsModel = new \App\Models\AppxApplicationDetailsModel();
    }

    /**
     * Display the list of unacknowledged applications
     */
    public function index()
    {
        // Get real applications data from database using model method
        $applications = $this->applicationsModel->getUnacknowledgedApplicationsWithDetails();

        // Format contact details for display
        foreach ($applications as &$application) {
            // Parse contact_details JSON if it exists
            if (!empty($application['contact_details'])) {
                $contacts = json_decode($application['contact_details'], true);
                if (is_array($contacts)) {
                    $application['email'] = $contacts['email'] ?? '';
                    $application['phone'] = $contacts['phone'] ?? '';
                    $application['contact_display'] = $contacts['phone'] ?? '';
                } else {
                    // If it's not JSON, treat as plain text
                    $application['contact_display'] = $application['contact_details'];
                    $application['email'] = '';
                    $application['phone'] = '';
                }
            } else {
                $application['contact_display'] = '';
                $application['email'] = '';
                $application['phone'] = '';
            }

            // Set display names for compatibility with view
            $application['fname'] = $application['first_name'];
            $application['lname'] = $application['last_name'];
            $application['recieved_acknowledged'] = $application['received_at'];

            // Set department from organization name if not available
            $application['department'] = $application['org_name'] ?? 'N/A';
        }

        $data = [
            'title' => 'Incoming Applications',
            'menu' => 'applications',
            'applications' => $applications
        ];

        return view('incoming_applicants/incoming_applicants_index', $data);
    }

    /**
     * Mark an application as received/acknowledged and send email notification
     */
    public function acknowledge($id)
    {
        $userId = $this->session->get('user_id') ?? 1;

        $data = [
            'is_received' => 1,
            'received_status' => 'acknowledged',
            'received_by' => $userId,
            'received_at' => date('Y-m-d H:i:s'),
            'application_status' => 'active',
            'updated_by' => $userId
        ];

        try {
            // Update the application in database
            $updateSuccess = $this->applicationsModel->update($id, $data);

            if ($updateSuccess) {
                log_message('info', 'Application acknowledged: ID ' . $id);

                // Get application details with full data
                $application = $this->applicationsModel->getApplicationWithDetails($id);

                if (!$application) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Application not found after acknowledgment',
                        'csrf_hash' => csrf_hash()
                    ]);
                }

                // Send acknowledgment email
                $emailResult = $this->sendAcknowledgmentEmail($application);

                // Return result
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Application successfully acknowledged and email sent',
                    'email_sent' => $emailResult['success'],
                    'email_message' => $emailResult['message'],
                    'csrf_hash' => csrf_hash()
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to acknowledge application',
                    'csrf_hash' => csrf_hash()
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Error acknowledging application: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    /**
     * Send acknowledgment email to applicant
     */
    private function sendAcknowledgmentEmail($application, $email)
    {
        try {
            if (empty($email)) {
                log_message('warning', 'No email address found for application: ' . $application['application_number']);
                return false;
            }

            $emailService = \Config\Services::email();

            $subject = 'Application Received - ' . $application['application_number'];
            $message = "
                <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #dc3545 0%, #000000 100%); color: white; padding: 20px;'>
                    <h2 style='color: #ffd700; text-align: center;'>DERS - Application Acknowledgment</h2>

                    <p>Dear {$application['first_name']} {$application['last_name']},</p>

                    <p>We acknowledge receipt of your application for the position:</p>

                    <div style='background: rgba(255,255,255,0.1); padding: 15px; margin: 20px 0; border-radius: 5px;'>
                        <strong>Position:</strong> {$application['position_title']}<br>
                        <strong>Organization:</strong> {$application['org_name']}<br>
                        <strong>Application Number:</strong> {$application['application_number']}<br>
                        <strong>Date Received:</strong> " . date('d M Y, h:i A') . "
                    </div>

                    <p>Your application is now being processed. You will be notified of any updates regarding your application status.</p>

                    <p>Thank you for your interest in this position.</p>

                    <p style='margin-top: 30px;'>
                        Best regards,<br>
                        <strong style='color: #ffd700;'>DERS Team</strong><br>
                        Dakoii Echad Recruitment & Selection System
                    </p>
                </div>
            ";

            $emailService->setTo($email);
            $emailService->setSubject($subject);
            $emailService->setMessage($message);

            $result = $emailService->send();

            if ($result) {
                log_message('info', 'Acknowledgment email sent to: ' . $email . ' for application: ' . $application['application_number']);
                return true;
            } else {
                log_message('error', 'Failed to send acknowledgment email to: ' . $email . ' - ' . $emailService->printDebugger());
                return false;
            }
        } catch (\Exception $e) {
            log_message('error', 'Exception while sending acknowledgment email: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * View application details
     */
    public function view($id)
    {
        // Get real application data from database using model method
        $application = $this->applicationsModel->getApplicationWithDetails($id);

        if (!$application) {
            return redirect()->to('incoming_applications')->with('error', 'Application not found');
        }

        // Parse contact details for display
        if (!empty($application['contact_details'])) {
            $contacts = json_decode($application['contact_details'], true);
            if (is_array($contacts)) {
                $application['email'] = $contacts['email'] ?? '';
                $application['phone'] = $contacts['phone'] ?? '';
                $application['contact_display'] = $contacts['phone'] ?? '';
            } else {
                $application['contact_display'] = $application['contact_details'];
                $application['email'] = '';
                $application['phone'] = '';
            }
        } else {
            $application['contact_display'] = '';
            $application['email'] = '';
            $application['phone'] = '';
        }

        // Set display names for compatibility with view
        $application['fname'] = $application['first_name'];
        $application['lname'] = $application['last_name'];
        $application['recieved_acknowledged'] = $application['received_at'];
        $application['dobirth'] = $application['date_of_birth'];

        // Set department from organization name
        $application['department'] = $application['org_name'] ?? 'N/A';

        // Get application files
        $appxFilesModel = new \App\Models\AppxApplicationFilesModel();
        $applicationFiles = $appxFilesModel->where('application_id', $id)->findAll();

        // Set file paths for compatibility and organize files
        foreach ($applicationFiles as $file) {
            if (strpos($file['file_title'], 'CV') !== false || strpos($file['file_title'], 'Resume') !== false) {
                $application['cv_path'] = $file['file_path'];
            } elseif (strpos($file['file_title'], 'Cover') !== false) {
                $application['cover_letter_path'] = $file['file_path'];
            }
        }

        $data = [
            'title' => 'Application Details - ' . $application['application_number'],
            'menu' => 'applications',
            'application' => $application,
            'applicationFiles' => $applicationFiles
        ];

        return view('incoming_applicants/incoming_applicants_view', $data);
    }

    /**
     * Batch acknowledge multiple applications
     */
    public function batchAcknowledge()
    {
        $ids = $this->request->getPost('ids');

        if (empty($ids)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No applications selected',
                'csrf_hash' => csrf_hash()
            ]);
        }

        $data = [
            'is_received' => 1,
            'received_status' => 'acknowledged',
            'received_by' => $this->session->get('user_id') ?? 1,
            'received_at' => date('Y-m-d H:i:s'),
            'application_status' => 'active',
            'updated_by' => $this->session->get('user_id') ?? 1
        ];

        $successCount = 0;
        $failCount = 0;
        $emailSentCount = 0;

        foreach ($ids as $id) {
            try {
                // Update the application in database
                $updateSuccess = $this->applicationsModel->update($id, $data);

                if ($updateSuccess) {
                    $successCount++;

                    // Get application data for email notification
                    $application = $this->applicationsModel->select('
                            appx_application_details.*,
                            positions.designation as position_title,
                            dakoii_org.org_name
                        ')
                        ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
                        ->join('dakoii_org', 'appx_application_details.org_id = dakoii_org.id', 'left')
                        ->where('appx_application_details.id', $id)
                        ->first();

                    if ($application) {
                        // Parse contact details to get email
                        $email = '';
                        if (!empty($application['contact_details'])) {
                            $contacts = json_decode($application['contact_details'], true);
                            if (is_array($contacts) && isset($contacts['email'])) {
                                $email = $contacts['email'];
                            }
                        }

                        // Send acknowledgment email
                        $emailSent = $this->sendAcknowledgmentEmail($application, $email);
                        if ($emailSent) $emailSentCount++;
                    }
                } else {
                    $failCount++;
                }
            } catch (\Exception $e) {
                log_message('error', 'Error in batch acknowledge for ID ' . $id . ': ' . $e->getMessage());
                $failCount++;
            }
        }

        return $this->response->setJSON([
            'success' => true,
            'message' => "$successCount applications acknowledged successfully. $failCount failed. $emailSentCount email notifications sent.",
            'csrf_hash' => csrf_hash()
        ]);
    }
}